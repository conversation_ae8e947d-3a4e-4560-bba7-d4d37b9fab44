<template>
    <ut-page>
        <ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
            <f-navbar fontColor="#fff" :bgColor="colors" title="校对异常处理" navbarType="1"></f-navbar>
        </ut-top>

        <view class="padding-lr-xs">
            <!-- 客户信息 -->
            <view class="cu-card margin-lr-sm margin-tb-sm radius bg-white flex padding-sm">
                <view class="flex-sub-0 margin-right-sm">
                    <u-lazy-load v-if="form.imgHead"
                        :image="$tools.showImg(form.imgHead)"
                        width="120"
                        height="160"
                        border-radius="4" />
                </view>
                <view class="flex-sub text-content">
                    <view class="flex justify-between align-center">
                        <view>
                            <view class="flex justify-start align-center">
                                <view class="text-df text-bold text-black">{{ form.name }}</view>
                                <view class="text-sm text-blue margin-left-lg" v-if="form.sex == 1">男
                                    <text class="cuIcon-male" />
                                </view>
                                <view class="text-sm text-pink margin-left-lg" v-if="form.sex == 2">女
                                    <text class="cuIcon-female" />
                                </view>
                            </view>
                            <view class="text-sm text-gray margin-top-xs">
                                <text v-if="form.phone" class="cuIcon-phone margin-right-xs" />
                                {{ form.phone }}
                            </view>
                        </view>
                        <view class="text-right">
                            <view class="text-sm text-gray">护理日期</view>
                            <view class="text-df text-bold" :style="{ color: colors }">{{ form.workDate }}</view>
                        </view>
                    </view>

                    <view class="text-xs text-gray text-cut margin-top-xs">{{ form.address }}</view>
                    <view v-if="form.attendantName" class="margin-top-xs">
                        <text class="text-sm" :style="{ color: colors }">({{ form.groupName }})</text>
                        <text class="text-sm text-gray">护理员：{{ form.attendantName }}</text>
                    </view>

                    <view v-if="form.idcard" class="flex align-center margin-top-xs">
                        <text class="text-xs text-gray margin-right-xs">证件号码：</text>
                        <text class="text-xs text-black">{{ form.idcard }}</text>
                    </view>

                    <!-- 额外信息 -->
                    <view v-if="form.schedulingDuration" class="flex align-center margin-top-xs">
                        <text class="text-xs text-gray margin-right-xs">排班时长：</text>
                        <text class="text-xs text-black">{{ form.schedulingDuration }}</text>
                    </view>

                    <view v-if="form.totalDuration" class="flex align-center margin-top-xs">
                        <text class="text-xs text-gray margin-right-xs">实际时长：</text>
                        <text class="text-xs text-black">{{ form.totalDuration }}</text>
                    </view>

                    <view v-if="form.checkInTime" class="flex align-center margin-top-xs">
                        <text class="text-xs text-gray margin-right-xs">签到时间：</text>
                        <text class="text-xs text-black">{{ form.checkInTime }}</text>
                    </view>

                    <view v-if="form.checkOutTime" class="flex align-center margin-top-xs">
                        <text class="text-xs text-gray margin-right-xs">签退时间：</text>
                        <text class="text-xs text-black">{{ form.checkOutTime }}</text>
                    </view>

                    <view v-if="form.lastManualUserName" class="flex align-center margin-top-xs">
                        <text class="text-xs text-gray margin-right-xs">校对人：</text>
                        <text class="text-xs text-black">{{ form.lastManualUserName }}</text>
                    </view>

                    <view v-if="form.lastManualTime" class="flex align-center margin-top-xs">
                        <text class="text-xs text-gray margin-right-xs">校对时间：</text>
                        <text class="text-xs text-black">{{ form.lastManualTime }}</text>
                    </view>

                    <view v-if="form.proofreadErrorRemark" class="margin-top-xs">
                        <text class="text-sm text-gray">校对异常信息：</text>
                        <text class="cu-tag sm bg-orange light radius">
                            {{ form.proofreadErrorRemark }}
                        </text>
                    </view>
                </view>
            </view>

            <!-- 服务项目 -->
            <view class="content-section">
                <view class="section-header">
                    <view class="section-title">服务项目</view>
                    <view>
                        <u-button type="primary"
                            :color="colors"
                            text="增加服务项目"
                            :plain="true"
                            size="mini"
                            @click="addProject" />
                    </view>
                </view>
                <view v-if="form.projects && form.projects.length > 0">
                    <u-swipe-action ref="swipeUserList">
                        <u-swipe-action-item v-for="(item, index) in form.projects" :key="index"
                            :name="item.id"
                            :options="getProjectActionOption(item)"
                            @click="projectActionOp">
                            <view class="padding-lr padding-tb-sm">
                                <project-item :detail="item" :colors="colors" @select="select">
                                    <template #op>
                                        <u-button type="primary"
                                            shape="circle"
                                            :color="colors"
                                            text="查看"
                                            :plain="false"
                                            size="small"
                                            @tap="select(item)" />
                                    </template>
                                </project-item>
                            </view>
                        </u-swipe-action-item>
                    </u-swipe-action>
                </view>
                <view v-else class="empty-state">
                    <text>暂无服务项目</text>
                </view>
            </view>

            <!-- 服务资料 -->
            <view v-if="datas && datas.length > 0" class="content-section">
                <view class="section-header">
                    <view class="section-title">服务资料</view>
                </view>
                <scroll-view scroll-y="true" class="scroll-box">
                    <view v-for="(item, index) in datas" :key="index" class="bg-white text-content item-box">
                        <view class="text-bold text-lg">{{ item.title }}</view>
                        <view v-for="(detail, dIndex) in item.details" :key="dIndex" class="padding-left-lg">
                            <view class="margin-left-xs">
                                <text>{{ detail.title }}</text>
                                <text v-if="detail.require"
                                    class="text-xs margin-left-xs"
                                    :style="{ color: colors }">(必填)
                                </text>
                                <text v-else class="text-xs margin-left-xs">(选填)</text>
                            </view>
                            <view class="image-box">
                                <ut-image-upload
                                    ref="upload"
                                    name="file"
                                    v-model="detail.files"
                                    mediaType="image"
                                    :colors="colors"
                                    :max="20"
                                    :headers="headers"
                                    :action="uploadInfo.server + uploadInfo.single || ''"
                                    :preview-image-width="1200"
                                    :width="200"
                                    :height="160"
                                    :border-radius="8"
                                    :disabled="false"
                                    :add="true"
                                    :remove="true"
                                    @uploadSuccess="uploadFaceSuccess($event, detail)">
                                </ut-image-upload>
                            </view>
                        </view>
                    </view>
                </scroll-view>
            </view>


            <!-- 底部按钮 -->
            <view class="footer-buttons">
                <u-button
                    type="primary"
                    text="确定"
                    @click="save"
                    :loading="loading"
                ></u-button>
                <u-button
                    text="返回"
                    @click="goBack"
                ></u-button>
            </view>

        </view>

        <!-- 项目选择弹窗 -->
        <u-popup :show="showProject"
            mode="bottom"
            round="10"
            :closeable="true"
            :safe-area-inset-bottom="false"
            :mask-close-able="true"
            close-icon-pos="top-left"
            :z-index="998"
            :overlay-style="{zIndex:998}"
            @close="showProject=false">
            <view class="pop-title">项目选择</view>
            <project :project-data="projectData" @selectProject="selectProject" />
        </u-popup>
    </ut-page>
</template>

<script>
let app = getApp()
import { mapState } from 'vuex'
import ProjectItem from '@/pagesA/components/project-item.vue'
import Project from '@/pagesA/care/scheduling/declare/project.vue'

export default {
    components: {
        ProjectItem,
        Project,
    },
    data() {
        return {
            colors: '',
            topWrapHeight: 0,
            workId: '',
            month: '',
            loading: false,
            form: {},
            datas: [],
            showProject: false,
            projectData: [],
        }
    },
    computed: {
        ...mapState({
            community: state => state.init.community,
            uploadInfo: state => state.init.oss,
        }),
        headers() {
            return {
                Token: this.uploadInfo.token,
            }
        },
    },
    onLoad(options) {
        this.workId = options.workId || ''
        this.month = options.month || ''
    },
    onShow() {
        this.setData({ colors: app.globalData.newColor })
        this.getInfo()
        this.getAllProject()
    },
    methods: {
        getHeight(height, statusHeight) {
            this.topWrapHeight = height
        },
        async getInfo() {
            if (!this.workId) return
            this.loading = true
            const params = {
                communityId: this.community.id,
                workId: this.workId,
            }
            if (this.month) params.month = this.month
            try {
                const { data } = await this.$ut.api('mang/care/proofread/task', params)
                this.form = {
                    ...this.form,
                    ...data,
                    projects: data.projects?.map((item) => ({
                        ...item,
                        id: item.projectId,
                        _id: item.id,
                    })),
                }
                this.datas = data.datas || []
                // 为每个 detail 初始化 files 数组
                this.datas.forEach(item => {
                    if (item.details && item.details.length > 0) {
                        item.details.forEach(detail => {
                            if (!detail.files) {
                                this.$set(detail, 'files', [])
                            }
                        })
                    }
                })
            } finally {
                this.loading = false
            }
        },
        async save() {
            this.loading = true
            try {
                await this.$ut.api('mang/care/proofread/errorSave', {
                    communityId: this.community.id,
                    workId: this.workId,
                    projectIds: this.form.projects?.map((item) => item.id || item.projectId),
                    datas: this.datas,
                })
                this.$u.toast('操作成功')
                this.goBack()
            } finally {
                this.loading = false
            }
        },

        goBack() {
            uni.navigateBack()
        },

        // 获取所有可选项目
        async getAllProject() {
            if (!this.workId) return
            try {
                const { data } = await this.$ut.api('mang/care/customer/scheduling/declare/project', {
                    communityId: this.community.id,
                    schedulingId: this.workId,
                    pagesize: 100,
                })
                this.projectData = data || []
            } catch (error) {
                console.error('获取项目列表失败:', error)
            }
        },

        // 添加服务项目
        addProject() {
            this.showProject = true

            if (this.projectData && this.form.projects && this.form.projects.length) {
                this.projectData.forEach(project => {
                    let obj = this.form.projects.find(u => u.id == project.id)
                    if (obj) {
                        this.$set(project, 'checked', true)
                    } else {
                        this.$set(project, 'checked', false)
                    }
                })
            }
        },

        // 选择项目
        selectProject(projects) {
            this.showProject = false

            if (!this.form.projects) this.$set(this.form, 'projects', [])
            if (!projects || !projects.length) return

            projects.forEach(project => {
                let obj = this.form.projects.find(u => u.id == project.id)
                if (!obj) {
                    this.form.projects.push({
                        id: project.id,
                        projectId: project.id,
                        projectName: project.name,
                        name: project.name,
                        govCode: project.govCode,
                        requireMinDuration: project.minDuration,
                        requireMaxDuration: project.maxDuration,
                    })
                }
            })
        },

        // 获取项目操作选项
        getProjectActionOption(item) {
            const btnDelete = {
                text: '删除项目',
                code: 'delete',
                style: {
                    backgroundColor: '#f56c6c',
                },
            }
            return [btnDelete]
        },

        // 项目操作
        projectActionOp(data) {
            if (data.code == 'delete') {
                if (this.form.projects && this.form.projects.length) {
                    var index = this.form.projects.findIndex(u => u.id == data.name)
                    if (index >= 0) this.form.projects.splice(index, 1)
                }
            }
            this.$refs['swipeUserList'].closeAll()
        },

        // 项目选择
        select(item) {
            // 可以在这里添加项目详情查看逻辑
            console.log('选择项目:', item)
        },

        // 图片上传成功回调
        uploadFaceSuccess(res, detail) {
            res.forEach(img => {
                const item = img.data
                detail.files.push(this.uploadInfo.preview + '?file=' + item.name + item.ext)
            })
        },
    },
}
</script>

<style lang="scss" scoped>
.content-section {
  background: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-header {
  padding: 30rpx 30rpx 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.project-list, .data-list {
  padding: 30rpx;

  .project-item, .data-item {
    padding: 25rpx;
    border: 1rpx solid #eee;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    background: #fafafa;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .project-name, .data-name {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 15rpx;
  }

  .project-detail, .data-detail {
    font-size: 26rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 10rpx;
  }

  .project-duration {
    margin-top: 10rpx;

    .duration-text {
      font-size: 24rpx;
      color: #999;
    }
  }

  .data-details {
    margin-top: 15rpx;

    .detail-item {
      display: flex;
      align-items: center;
      margin-bottom: 8rpx;

      .detail-title {
        font-size: 24rpx;
        color: #666;
        margin-right: 10rpx;
      }

      .detail-require {
        font-size: 22rpx;
        color: #f56c6c;
      }

      .detail-optional {
        font-size: 22rpx;
        color: #999;
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 80rpx 30rpx;
  color: #999;
  font-size: 28rpx;
}

.footer-buttons {
  background: white;
  padding: 30rpx;
  margin: 20rpx 0;
  border-radius: 12rpx;
  display: flex;
  gap: 20rpx;

  .u-button {
    flex: 1;
  }
}

.pop-title {
  padding-top: 20rpx;
  text-align: center;
}

.item-box {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.image-box {
  margin-top: 20rpx;
}


</style>
